import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

/**
 * Returns the appropriate contact URL based on the current environment
 * Uses window.location.hostname to determine if running in preview or production
 */
export function getContactUrl(): string {
	const hostname = window.location.hostname;

	// Check if we're in preview environment
	if (hostname.includes("preview") || hostname.includes("localhost")) {
		return "https://preview.parhlai.com/contact";
	}

	// Default to production URL
	return "https://parhlai.com/contact";
}

export const toggleMCQType = (type: string) => {
	const mapping: Record<string, string> = {
		cramming: "roha",
		roha: "cramming",
	};

	return mapping[type] ?? type;
};
